use tauri::{Manager, Window};

// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn toggle_always_on_top(window: Window) -> Result<(), String> {
    let is_always_on_top = window.is_always_on_top().map_err(|e| e.to_string())?;
    window.set_always_on_top(!is_always_on_top).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn set_window_position(window: Window, x: i32, y: i32) -> Result<(), String> {
    use tauri::Position;
    window.set_position(Position::Physical(tauri::PhysicalPosition { x, y }))
        .map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn set_window_opacity(window: Window, opacity: f64) -> Result<(), String> {
    // Note: This might not work on all platforms due to transparency limitations
    // We'll handle opacity through CSS for better cross-platform support
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            toggle_always_on_top,
            set_window_position,
            set_window_opacity
        ])
        .setup(|app| {
            let window = app.get_webview_window("main").unwrap();

            // Set initial window properties
            let _ = window.set_always_on_top(true);
            let _ = window.set_decorations(false);

            #[cfg(target_os = "macos")]
            {
                // macOS specific transparency and window level settings
                use tauri::TitleBarStyle;
                let _ = window.set_title_bar_style(TitleBarStyle::Overlay);
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
