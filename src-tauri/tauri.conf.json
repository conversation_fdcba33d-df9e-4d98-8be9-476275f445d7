{"$schema": "https://schema.tauri.app/config/2", "productName": "invisible-helper", "version": "0.1.0", "identifier": "com.invisible-helper.app", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "Invisible Helper", "width": 400, "height": 600, "minWidth": 300, "minHeight": 400, "transparent": true, "alwaysOnTop": true, "decorations": false, "resizable": true, "skipTaskbar": true, "visible": true, "center": false, "x": 100, "y": 100, "shadow": false, "titleBarStyle": "Overlay"}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}