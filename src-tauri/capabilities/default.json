{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "core:window:allow-set-always-on-top", "core:window:allow-set-decorations", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-is-always-on-top", "opener:default"]}