import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";

function App() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    setGreetMsg(await invoke("greet", { name }));
  }

  async function toggleAlwaysOnTop() {
    try {
      await invoke("toggle_always_on_top");
    } catch (error) {
      console.error("Failed to toggle always on top:", error);
    }
  }

  return (
    <main className="container">
      <h1>🕵️ Invisible Helper</h1>
      <p style={{ fontSize: '14px', opacity: 0.8, marginBottom: '20px' }}>
        Transparent Note-Taking Assistant
      </p>

      <div style={{ marginBottom: '20px' }}>
        <button onClick={toggleAlwaysOnTop} style={{ marginBottom: '10px' }}>
          Toggle Always On Top
        </button>
      </div>

      <form
        className="row"
        onSubmit={(e) => {
          e.preventDefault();
          greet();
        }}
        style={{ marginBottom: '20px' }}
      >
        <input
          id="greet-input"
          onChange={(e) => setName(e.currentTarget.value)}
          placeholder="Test transparency..."
          value={name}
        />
        <button type="submit">Test</button>
      </form>

      {greetMsg && (
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '10px',
          borderRadius: '8px',
          marginTop: '10px'
        }}>
          {greetMsg}
        </div>
      )}

      <div style={{
        marginTop: 'auto',
        fontSize: '12px',
        opacity: 0.6,
        textAlign: 'center'
      }}>
        Phase 1: Transparency & Window Management ✅
      </div>
    </main>
  );
}

export default App;
