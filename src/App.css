.logo.vite:hover {
  filter: drop-shadow(0 0 2em #747bff);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafb);
}
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color: #ffffff;
  background-color: transparent;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Transparent body and html */
html, body {
  background: transparent !important;
  margin: 0;
  padding: 0;
}

#root {
  background: transparent;
  min-height: 100vh;
}

.container {
  margin: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: calc(100vh - 40px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: 0.75s;
}

.logo.tauri:hover {
  filter: drop-shadow(0 0 2em #24c8db);
}

.row {
  display: flex;
  justify-content: center;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  text-align: center;
}

input,
button {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  transition: all 0.25s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

button {
  cursor: pointer;
}

button:hover {
  border-color: rgba(57, 108, 216, 0.8);
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}
button:active {
  border-color: rgba(57, 108, 216, 1);
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(0);
}

input,
button {
  outline: none;
}

#greet-input {
  margin-right: 5px;
}

/* Placeholder styles for input focus */
input:focus {
  border-color: rgba(57, 108, 216, 0.8);
  background-color: rgba(255, 255, 255, 0.15);
  outline: none;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
